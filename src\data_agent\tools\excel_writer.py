"""
Excel写入工具模块
提供协议价数据写入到Excel文件的功能
"""
import os
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, Type
from langchain.tools import BaseTool
from pydantic import BaseModel, Field

from ..utils.excel_utils import safe_excel_write, kill_excel_processes


class AgreementPriceInput(BaseModel):
    """协议价输入数据模型"""
    customer_id: str = Field(description="客户ID（必填）")
    product_id: str = Field(description="货品ID（必填）")
    agreement_price: float = Field(description="协议价（必填）")


class ExcelWriterTool(BaseTool):
    """Excel写入工具"""
    name: str = "write_agreement_price_to_excel"
    description: str = """
    将协议价数据写入到Excel文件（底表.xlsx）的工具。

    需要提供以下参数：
    - customer_id: 客户ID（必填）
    - product_id: 货品ID（必填）
    - agreement_price: 协议价（必填）

    工具会自动计算：
    - 开始日期：当前日期（YYYY/MM/DD格式）
    - 结束日期：开始日期+30天（YYYY/MM/DD格式）
    - 考核价：复制协议价的值
    - 建议零售价：复制协议价的值

    数据将写入到"协议价集合"工作表中。
    """
    args_schema: Type[BaseModel] = AgreementPriceInput
    
    def _run(self, customer_id: str, product_id: str, agreement_price: float) -> str:
        """执行Excel写入操作"""
        try:
            # 1. 准备数据
            current_date = datetime.now()
            start_date = current_date.strftime("%Y/%m/%d")
            end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
            
            # 2. 创建数据行
            new_row = {
                "客户ID": customer_id,
                "货品ID": product_id,
                "协议价": agreement_price,
                "开始日期": start_date,
                "结束日期": end_date,
                "考核价": agreement_price,  # 复制协议价的值
                "建议零售价": agreement_price  # 复制协议价的值
            }
            
            # 3. 确定文件路径
            file_path = Path("data/raw/底表.xlsx")
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 4. 读取现有数据或创建新的DataFrame
            sheet_name = "协议价集合"
            
            if file_path.exists():
                try:
                    # 尝试读取指定工作表
                    existing_df = pd.read_excel(file_path, sheet_name=sheet_name)
                except ValueError:
                    # 工作表不存在，创建新的DataFrame
                    existing_df = pd.DataFrame(columns=list(new_row.keys()))
                except Exception as e:
                    # 其他错误，创建新的DataFrame
                    existing_df = pd.DataFrame(columns=list(new_row.keys()))
            else:
                # 文件不存在，创建新的DataFrame
                existing_df = pd.DataFrame(columns=list(new_row.keys()))
            
            # 5. 添加新行
            new_df = pd.concat([existing_df, pd.DataFrame([new_row])], ignore_index=True)
            
            # 6. 写入Excel文件
            success = self._write_to_excel(new_df, file_path, sheet_name)
            
            if success:
                return f"""✅ 协议价数据已成功写入Excel文件！

📋 写入详情：
• 文件路径：{file_path}
• 工作表：{sheet_name}
• 客户ID：{customer_id}
• 货品ID：{product_id}
• 协议价：{agreement_price}
• 开始日期：{start_date}
• 结束日期：{end_date}
• 考核价：{agreement_price}
• 建议零售价：{agreement_price}

📊 当前工作表共有 {len(new_df)} 条记录。"""
            else:
                return f"❌ 写入Excel文件失败，请检查文件是否被其他程序占用。"
                
        except Exception as e:
            return f"❌ 处理协议价数据时发生错误：{str(e)}"
    
    def _write_to_excel(self, df: pd.DataFrame, file_path: Path, sheet_name: str) -> bool:
        """写入数据到Excel文件"""
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 如果文件存在，需要保留其他工作表
            if file_path.exists():
                try:
                    # 读取现有的所有工作表
                    with pd.ExcelFile(file_path) as xls:
                        existing_sheets = {}
                        for sheet in xls.sheet_names:
                            if sheet != sheet_name:  # 保留其他工作表
                                existing_sheets[sheet] = pd.read_excel(xls, sheet_name=sheet)

                    # 写入所有工作表（包括新的/更新的目标工作表）
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        # 写入目标工作表
                        df.to_excel(writer, sheet_name=sheet_name, index=False)

                        # 写入其他现有工作表
                        for sheet, data in existing_sheets.items():
                            data.to_excel(writer, sheet_name=sheet, index=False)
                except Exception as e:
                    # 如果读取现有文件失败，直接覆盖
                    print(f"读取现有文件失败，将创建新文件: {e}")
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
            else:
                # 文件不存在，直接创建
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

            return True

        except PermissionError as e:
            print(f"权限错误: {e}")
            # 文件被占用，尝试关闭Excel进程后重试
            kill_excel_processes()
            try:
                if file_path.exists():
                    try:
                        with pd.ExcelFile(file_path) as xls:
                            existing_sheets = {}
                            for sheet in xls.sheet_names:
                                if sheet != sheet_name:
                                    existing_sheets[sheet] = pd.read_excel(xls, sheet_name=sheet)

                        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                            for sheet, data in existing_sheets.items():
                                data.to_excel(writer, sheet_name=sheet, index=False)
                    except Exception:
                        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                else:
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                return True
            except Exception as e:
                print(f"重试后仍然失败: {e}")
                return False
        except Exception as e:
            print(f"其他错误: {e}")
            return False


def create_excel_writer_tool():
    """创建Excel写入工具实例"""
    return ExcelWriterTool()


if __name__ == '__main__':
    # 测试工具
    tool = create_excel_writer_tool()
    
    # 测试数据
    test_result = tool._run(
        customer_id="C001",
        product_id="P001", 
        agreement_price=99.99
    )
    
    print("测试结果：")
    print(test_result)
