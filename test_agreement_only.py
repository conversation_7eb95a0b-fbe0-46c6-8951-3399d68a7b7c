"""
专门测试协议价功能的脚本（不依赖数据库）
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_excel_tool_only():
    """仅测试Excel工具"""
    print("🧪 测试Excel协议价工具...")
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 创建工具
        tool = create_excel_writer_tool()
        print(f"✅ Excel工具创建成功: {tool.name}")
        
        # 测试数据
        test_cases = [
            {"customer_id": "C001", "product_id": "P001", "agreement_price": 99.99},
            {"customer_id": "C002", "product_id": "P002", "agreement_price": 150.50},
            {"customer_id": "C003", "product_id": "P003", "agreement_price": 75.25}
        ]
        
        print(f"\n📋 开始测试 {len(test_cases)} 条数据...")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔄 测试用例 {i}:")
            print(f"   客户ID: {test_case['customer_id']}")
            print(f"   货品ID: {test_case['product_id']}")
            print(f"   协议价: {test_case['agreement_price']}")
            
            try:
                result = tool._run(
                    customer_id=test_case["customer_id"],
                    product_id=test_case["product_id"],
                    agreement_price=test_case["agreement_price"]
                )
                
                print(f"📤 结果:")
                print(result)
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel工具测试失败: {e}")
        return False

def create_minimal_agent():
    """创建仅包含Excel工具的最小智能体"""
    print("\n🤖 创建最小智能体（仅Excel功能）...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.2
        )
        
        # 仅使用Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 创建系统提示词
        system_prompt = """
        You are a helpful assistant that specializes in processing agreement price data.
        
        When you receive agreement price data with Customer ID, Product ID, and Agreement Price, 
        use the Excel writer tool to save this data.
        
        The tool will automatically:
        - Set start date to current date (YYYY/MM/DD format)
        - Set end date to start date + 30 days (YYYY/MM/DD format)
        - Copy agreement price to assessment price (考核价)
        - Copy agreement price to suggested retail price (建议零售价)
        - Save data to "协议价集合" sheet in "底表.xlsx" file
        
        Always respond in Chinese and provide clear feedback about the operation.
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        print("✅ 最小智能体创建成功！")
        return agent
        
    except Exception as e:
        print(f"❌ 最小智能体创建失败: {e}")
        return None

def test_minimal_agent():
    """测试最小智能体"""
    print("\n💬 测试最小智能体...")
    
    agent = create_minimal_agent()
    if not agent:
        return False
    
    # 测试用例
    test_inputs = [
        """请帮我处理以下协议价数据：
客户ID: C001
货品ID: P001
协议价: 99.99""",
        
        """处理协议价信息：
客户ID: C002, 货品ID: P002, 协议价: 150.50""",
        
        "你好，请介绍一下你的功能"
    ]
    
    for i, test_input in enumerate(test_inputs, 1):
        print(f"\n📋 测试 {i}: {test_input[:30]}...")
        print(f"💬 输入: {test_input}")
        print(f"🤖 响应:")
        print("-" * 40)
        
        try:
            response = agent.invoke({
                "messages": [{"role": "user", "content": test_input}]
            })
            
            print(response["messages"][-1].content)
            print("-" * 40)
            print("✅ 测试完成")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    return True

def interactive_mode():
    """交互模式"""
    print("\n🎯 协议价功能交互模式")
    print("=" * 50)
    print("输入 'quit' 退出")
    
    agent = create_minimal_agent()
    if not agent:
        print("❌ 智能体创建失败")
        return
    
    print("✅ 智能体已准备就绪")
    print("\n💡 使用示例:")
    print("请帮我处理以下协议价数据：")
    print("客户ID: C001")
    print("货品ID: P001") 
    print("协议价: 99.99")
    
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print("🤖 Agent: ", end="", flush=True)
            response = agent.invoke({
                "messages": [{"role": "user", "content": user_input}]
            })
            print(response["messages"][-1].content)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {str(e)}")

def main():
    """主函数"""
    print("🚀 协议价功能专项测试")
    print("=" * 60)
    
    # 测试Excel工具
    success1 = test_excel_tool_only()
    
    if success1:
        print("\n🎉 Excel工具测试通过！")
        
        # 询问用户要进行哪种测试
        print("\n请选择测试模式:")
        print("1. 自动测试智能体")
        print("2. 交互模式")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            test_minimal_agent()
        elif choice == "2":
            interactive_mode()
        elif choice == "3":
            print("👋 再见！")
        else:
            print("❌ 无效选择")
    else:
        print("\n❌ Excel工具测试失败")

if __name__ == '__main__':
    main()
