"""
完全修复编码问题的启动脚本
"""
import sys
import os
import locale
import codecs
from pathlib import Path

# 强制设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'

# 重新配置标准输出
if hasattr(sys.stdout, 'reconfigure'):
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='ignore')
        sys.stderr.reconfigure(encoding='utf-8', errors='ignore')
    except:
        pass

# 设置默认编码
if hasattr(sys, 'setdefaultencoding'):
    sys.setdefaultencoding('utf-8')

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    """安全打印，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果编码失败，使用ASCII安全模式
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)
    except Exception:
        print("[输出编码错误]")

def create_agent():
    """创建智能体"""
    safe_print("正在创建智能体...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        
        # 完全禁用LangSmith追踪
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        os.environ.pop("LANGCHAIN_API_KEY", None)
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.1  # 降低温度提高稳定性
        )
        
        # 使用Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 简化的系统提示词（避免复杂中文）
        system_prompt = """
        You are an assistant that processes agreement price data.
        
        When you see commands with "客户ID为", "货品ID为", "价格为", and "开始执行脚本":
        1. Extract customer_id (after 客户ID为)
        2. Extract product_id (after 货品ID为)  
        3. Extract agreement_price (after 价格为)
        4. Call write_agreement_price_to_excel tool immediately
        
        Examples:
        - "客户ID为002 货品ID为003 价格为3 开始执行脚本"
        - "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本"
        
        Always respond in simple Chinese and execute immediately.
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        safe_print("智能体创建成功！")
        return agent
        
    except Exception as e:
        safe_print(f"智能体创建失败: {str(e)}")
        return None

def test_command_directly():
    """直接测试指令"""
    safe_print("直接测试Excel工具...")
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        tool = create_excel_writer_tool()
        
        # 测试用户的指令数据
        result = tool._run(
            customer_id="002",
            product_id="003",
            agreement_price=3.0
        )
        
        safe_print("Excel工具测试结果:")
        safe_print(result)
        return True
        
    except Exception as e:
        safe_print(f"Excel工具测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    safe_print("Data Agent 编码修复版启动")
    safe_print("=" * 50)
    
    # 先直接测试Excel工具
    safe_print("步骤1: 测试Excel工具...")
    excel_success = test_command_directly()
    
    if not excel_success:
        safe_print("Excel工具测试失败，请检查配置")
        return
    
    safe_print("\n步骤2: 创建智能体...")
    agent = create_agent()
    if not agent:
        safe_print("智能体创建失败")
        return
    
    safe_print("\n触发指令格式:")
    safe_print("客户ID为XXX 货品ID为XXX 价格为XXX 开始执行脚本")
    safe_print("或")
    safe_print("客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本")
    
    safe_print("\n" + "=" * 50)
    safe_print("Data Agent 交互模式")
    safe_print("输入 'quit' 退出")
    safe_print("=" * 50)
    
    # 交互循环
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                safe_print("再见！")
                break
            
            if not user_input:
                continue
            
            safe_print("Agent: 正在处理...")
            
            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": user_input}]
                })
                
                # 安全地输出响应
                result = response["messages"][-1].content
                safe_print(result)
                
            except UnicodeEncodeError:
                safe_print("处理完成，请检查Excel文件。")
            except Exception as e:
                safe_print(f"处理错误: {str(e)}")
                
        except KeyboardInterrupt:
            safe_print("\n再见！")
            break
        except Exception as e:
            safe_print(f"输入错误: {str(e)}")

if __name__ == '__main__':
    main()
