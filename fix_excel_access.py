"""
修复Excel文件访问问题
"""
import os
import sys
import time
import psutil
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def kill_excel_processes():
    """强制关闭所有Excel进程"""
    print("🔧 正在关闭Excel进程...")
    
    killed_count = 0
    for proc in psutil.process_iter(['name', 'pid']):
        try:
            if proc.info['name'] and 'excel' in proc.info['name'].lower():
                print(f"   关闭进程: {proc.info['name']} (PID: {proc.info['pid']})")
                proc.kill()
                killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if killed_count > 0:
        print(f"✅ 已关闭 {killed_count} 个Excel进程")
        time.sleep(2)  # 等待进程完全关闭
    else:
        print("ℹ️ 未发现运行中的Excel进程")

def check_file_access():
    """检查文件访问权限"""
    file_path = Path("data/raw/底表.xlsx")
    
    print(f"🔍 检查文件访问权限: {file_path}")
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 检查读权限
    try:
        with open(file_path, 'rb') as f:
            f.read(1)
        print("✅ 文件读取权限正常")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return False
    
    # 检查写权限
    try:
        # 尝试以追加模式打开（不会破坏文件）
        with open(file_path, 'r+b') as f:
            pass
        print("✅ 文件写入权限正常")
        return True
    except Exception as e:
        print(f"❌ 文件写入权限失败: {e}")
        return False

def test_excel_tool_with_retry():
    """重试测试Excel工具"""
    print("\n🧪 测试Excel工具（带重试）...")
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        tool = create_excel_writer_tool()
        
        # 测试用户的数据
        print("📝 测试数据: 客户ID=002, 货品ID=003, 价格=3.0")
        
        max_retries = 3
        for attempt in range(max_retries):
            print(f"\n🔄 尝试 {attempt + 1}/{max_retries}...")
            
            try:
                result = tool._run(
                    customer_id="002",
                    product_id="003",
                    agreement_price=3.0
                )
                
                print("📤 测试结果:")
                print(result)
                
                if "成功" in result:
                    return True
                
            except Exception as e:
                print(f"❌ 尝试 {attempt + 1} 失败: {e}")
                
                if attempt < max_retries - 1:
                    print("🔧 关闭Excel进程后重试...")
                    kill_excel_processes()
                    time.sleep(1)
        
        print("❌ 所有重试都失败了")
        return False
        
    except Exception as e:
        print(f"❌ Excel工具创建失败: {e}")
        return False

def create_test_file():
    """创建测试用的Excel文件副本"""
    print("\n📋 创建测试文件...")
    
    try:
        import pandas as pd
        import shutil
        
        original_file = Path("data/raw/底表.xlsx")
        test_file = Path("data/raw/底表_测试.xlsx")
        
        if original_file.exists():
            # 复制原文件
            shutil.copy2(original_file, test_file)
            print(f"✅ 已创建测试文件: {test_file}")
            
            # 测试写入测试文件
            print("🧪 测试写入测试文件...")
            
            # 读取协议价集合工作表
            df = pd.read_excel(test_file, sheet_name="协议价集合")
            print(f"📊 原始数据行数: {len(df)}")
            
            # 添加测试数据
            from datetime import datetime, timedelta
            current_date = datetime.now()
            start_date = current_date.strftime("%Y/%m/%d")
            end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
            
            new_row = {}
            for col in df.columns:
                if col == "客户ID(必填)":
                    new_row[col] = "002"
                elif col == "货品ID(必填)":
                    new_row[col] = "003"
                elif col == "协议价(必填)":
                    new_row[col] = 3.0
                elif col == "开始日期(必填)":
                    new_row[col] = start_date
                elif col == "结束日期(必填)":
                    new_row[col] = end_date
                elif col == "考核价":
                    new_row[col] = 3.0
                elif col == "建议零售价":
                    new_row[col] = 3.0
                else:
                    new_row[col] = ""
            
            # 添加新行
            new_df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            
            # 写入测试文件
            with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
                new_df.to_excel(writer, sheet_name='协议价集合', index=False)
            
            print(f"✅ 测试文件写入成功，新数据行数: {len(new_df)}")
            print(f"📋 新增数据: 客户ID=002, 货品ID=003, 价格=3.0")
            
            return True
            
        else:
            print(f"❌ 原始文件不存在: {original_file}")
            return False
            
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Excel文件访问问题修复工具")
    print("=" * 60)
    
    # 步骤1: 关闭Excel进程
    kill_excel_processes()
    
    # 步骤2: 检查文件访问权限
    access_ok = check_file_access()
    
    if not access_ok:
        print("\n⚠️ 文件访问有问题，尝试创建测试文件...")
        test_success = create_test_file()
        if test_success:
            print("\n✅ 测试文件创建成功，Excel写入功能正常")
        else:
            print("\n❌ 测试文件创建失败")
        return
    
    # 步骤3: 测试Excel工具
    tool_success = test_excel_tool_with_retry()
    
    if tool_success:
        print("\n🎉 Excel工具测试成功！")
        print("\n📋 现在可以使用以下指令:")
        print("客户ID为002 货品ID为003 价格为3 开始执行脚本")
    else:
        print("\n❌ Excel工具测试失败")
        print("\n🔧 建议:")
        print("1. 确保Excel文件未被其他程序打开")
        print("2. 检查文件权限")
        print("3. 尝试重启程序")

if __name__ == '__main__':
    main()
