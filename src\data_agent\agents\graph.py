"""
智能体图模块
定义和创建LangGraph智能体
"""
import os
from dotenv import load_dotenv

from langgraph.prebuilt import create_react_agent
from langchain_deepseek import ChatDeepSeek

# 从我们自己的工具模块中导入工具
from ..tools.all_tools import get_all_tools

# 加载环境变量
load_dotenv()


def create_agent_executor():
    """创建智能体执行器"""
    # 1. 设置LLM模型
    # 这是智能体的大脑，用于思考和决策
    llm = ChatDeepSeek(
        model="deepseek-chat",
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        temperature=0.2  # 思考时可以有一点创造性
    )
    
    # 2. 创建系统提示词
    # 这是我们指导Agent如何思考和行动的指令
    system_prompt = """
    You are a helpful assistant named 'Data Agent'.
    You have access to a set of tools to answer user questions about the database, general information, and handle agreement price data.

    When you receive a question or request:
    1. If it's about data in the database (like employee information, salaries, departments), use the SQL tools to query the database
    2. If it's a general question not related to the database, use the web search tool
    3. If the user provides agreement price script trigger command, use the Excel writer tool to save data to the "底表.xlsx" file
    4. Always provide clear and helpful responses in Chinese

    IMPORTANT: Agreement Price Script Trigger Format
    When you see commands containing "客户ID为", "货品ID为", "价格为", and "开始执行脚本":
    You MUST:
    1. Extract the three values: 客户ID, 货品ID, 价格
    2. Immediately call the write_agreement_price_to_excel tool with these parameters:
       - customer_id: the value after "客户ID为"
       - product_id: the value after "货品ID为"
       - agreement_price: the numeric value after "价格为"
    3. Do not ask for confirmation, execute immediately

    Examples of trigger commands (flexible format):
    - "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本"
    - "客户ID为002 货品ID为003 价格为3 开始执行脚本"
    - "客户ID为CUST123,货品ID为PROD456,价格为150.50,开始执行脚本"

    The Excel writer tool will automatically:
    - Write data to the existing "协议价集合" sheet following exact column headers
    - Set start date to current date (YYYY/MM/DD format)
    - Set end date to start date + 30 days (YYYY/MM/DD format)
    - Copy agreement price to 考核价 and 建议零售价
    - Leave other fields empty
    - Preserve all other worksheets unchanged

    You are an expert at:
    - Writing and executing SQL queries
    - Analyzing database results
    - Searching for general information online
    - Processing agreement price script commands and writing to Excel files
    - Providing clear explanations in Chinese
    """
    
    # 3. 获取所有工具
    all_tools = get_all_tools()
    
    # 4. 使用 LangGraph 的 create_react_agent 来创建智能体执行器
    # 这个函数会自动将LLM和Tools组装成一个可以工作的ReAct (Reason+Act) Agent
    # ReAct是目前最主流的Agent工作模式
    agent_executor = create_react_agent(
        model=llm,
        tools=all_tools,
        prompt=system_prompt
    )
    
    return agent_executor


# 延迟创建智能体执行器实例
_agent_executor = None

def get_agent_executor():
    """获取智能体执行器（延迟创建）"""
    global _agent_executor
    if _agent_executor is None:
        _agent_executor = create_agent_executor()
    return _agent_executor

# 为了向后兼容，保留这个属性
@property
def agent_executor():
    return get_agent_executor()

# 将属性添加到模块
import sys
sys.modules[__name__].agent_executor = property(lambda self: get_agent_executor())


# (可选) 如果你想看一个简单的调用例子
if __name__ == '__main__':
    print("Agent Executor Created. Testing with a query...")
    
    # 模拟一个用户输入
    test_input = "技术部有多少人？他们的平均薪水是多少？"
    
    # 调用 agent (LangGraph 使用 messages 格式)
    response = agent_executor.invoke({
        "messages": [{"role": "user", "content": test_input}]
    })
    
    print("\n----- Agent Response -----")
    print(response["messages"][-1].content)
