"""
快速测试脚本 - 测试协议价功能
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试工具导入
        from data_agent.tools.excel_writer import create_excel_writer_tool
        from data_agent.tools.all_tools import get_all_tools
        
        print("✅ 工具导入成功")
        
        # 测试工具创建
        excel_tool = create_excel_writer_tool()
        all_tools = get_all_tools()
        
        print(f"✅ Excel工具创建成功: {excel_tool.name}")
        print(f"✅ 工具集合加载成功，共 {len(all_tools)} 个工具")
        
        # 查找Excel工具
        excel_found = any(tool.name == "write_agreement_price_to_excel" for tool in all_tools)
        if excel_found:
            print("✅ Excel工具已集成到工具集合")
        else:
            print("❌ Excel工具未找到")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_agent_creation():
    """测试智能体创建"""
    print("\n🤖 测试智能体创建...")
    
    try:
        from data_agent.agents.graph import get_agent_executor
        
        agent = get_agent_executor()
        print("✅ 智能体创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体创建失败: {e}")
        return False

def interactive_test():
    """交互式测试"""
    print("\n💬 开始交互式测试...")
    print("=" * 50)
    
    try:
        from data_agent.agents.graph import get_agent_executor
        
        agent = get_agent_executor()
        
        # 测试用例
        test_cases = [
            {
                "name": "协议价数据处理",
                "input": """请帮我处理以下协议价数据：
客户ID: C001
货品ID: P001
协议价: 99.99"""
            },
            {
                "name": "简单问候",
                "input": "你好，请介绍一下你的功能"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {test_case['name']}")
            print(f"💬 输入: {test_case['input']}")
            print(f"🤖 智能体响应:")
            print("-" * 30)
            
            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": test_case['input']}]
                })
                
                print(response["messages"][-1].content)
                print("-" * 30)
                print("✅ 测试完成")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 交互式测试失败: {e}")
        return False

def manual_interactive():
    """手动交互模式"""
    print("\n🎯 手动交互模式")
    print("=" * 50)
    print("输入 'quit' 退出")
    
    try:
        from data_agent.agents.graph import get_agent_executor
        
        agent = get_agent_executor()
        print("✅ 智能体已准备就绪")
        
        while True:
            try:
                user_input = input("\n用户: ").strip()
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                print("🤖 Agent: ", end="", flush=True)
                response = agent.invoke({
                    "messages": [{"role": "user", "content": user_input}]
                })
                print(response["messages"][-1].content)
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {str(e)}")
    
    except Exception as e:
        print(f"❌ 手动交互模式启动失败: {e}")

def main():
    """主函数"""
    print("🚀 Data Agent 快速测试")
    print("=" * 60)
    
    # 基本功能测试
    success1 = test_basic_functionality()
    
    # 智能体创建测试
    success2 = test_agent_creation()
    
    if success1 and success2:
        print("\n🎉 所有基础测试通过！")
        
        # 询问用户要进行哪种测试
        print("\n请选择测试模式:")
        print("1. 自动测试用例")
        print("2. 手动交互模式")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            interactive_test()
        elif choice == "2":
            manual_interactive()
        elif choice == "3":
            print("👋 再见！")
        else:
            print("❌ 无效选择")
    else:
        print("\n❌ 基础测试失败，请检查配置")

if __name__ == '__main__':
    main()
