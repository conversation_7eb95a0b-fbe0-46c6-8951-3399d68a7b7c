"""
测试Excel写入工具的功能
"""
import os
import sys
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from data_agent.tools.excel_writer import create_excel_writer_tool


def test_excel_writer_tool():
    """测试Excel写入工具"""
    print("🧪 开始测试Excel写入工具...")
    
    # 创建工具实例
    tool = create_excel_writer_tool()
    
    # 测试数据
    test_cases = [
        {
            "customer_id": "C001",
            "product_id": "P001",
            "agreement_price": 99.99,
            "description": "第一条测试数据"
        },
        {
            "customer_id": "C002", 
            "product_id": "P002",
            "agreement_price": 150.50,
            "description": "第二条测试数据"
        },
        {
            "customer_id": "C003",
            "product_id": "P003", 
            "agreement_price": 75.25,
            "description": "第三条测试数据"
        }
    ]
    
    print(f"\n📋 准备测试 {len(test_cases)} 条数据...")
    
    # 清理之前的测试文件
    test_file = Path("data/raw/底表.xlsx")
    if test_file.exists():
        try:
            os.remove(test_file)
            print(f"🗑️ 已删除之前的测试文件: {test_file}")
        except Exception as e:
            print(f"⚠️ 删除测试文件失败: {e}")
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔄 执行测试用例 {i}: {test_case['description']}")
        
        result = tool._run(
            customer_id=test_case["customer_id"],
            product_id=test_case["product_id"],
            agreement_price=test_case["agreement_price"]
        )
        
        print(f"📤 测试结果:")
        print(result)
        
        # 验证文件是否创建
        if test_file.exists():
            print(f"✅ 文件创建成功: {test_file}")
        else:
            print(f"❌ 文件创建失败: {test_file}")
            continue
    
    # 验证最终结果
    print(f"\n🔍 验证最终结果...")
    
    if test_file.exists():
        try:
            # 读取Excel文件
            df = pd.read_excel(test_file, sheet_name="协议价集合")
            
            print(f"📊 Excel文件验证结果:")
            print(f"• 工作表名称: 协议价集合")
            print(f"• 数据行数: {len(df)}")
            print(f"• 列数: {len(df.columns)}")
            print(f"• 列名: {list(df.columns)}")
            
            print(f"\n📋 数据内容预览:")
            print(df.to_string(index=False))
            
            # 验证数据完整性
            expected_columns = ["客户ID", "货品ID", "协议价", "开始日期", "结束日期", "考核价", "建议零售价"]
            missing_columns = [col for col in expected_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ 缺少列: {missing_columns}")
            else:
                print(f"✅ 所有必需列都存在")
            
            # 验证数据类型和值
            for index, row in df.iterrows():
                print(f"\n🔍 验证第 {index + 1} 行数据:")
                print(f"• 客户ID: {row['客户ID']}")
                print(f"• 货品ID: {row['货品ID']}")
                print(f"• 协议价: {row['协议价']}")
                print(f"• 开始日期: {row['开始日期']}")
                print(f"• 结束日期: {row['结束日期']}")
                print(f"• 考核价: {row['考核价']}")
                print(f"• 建议零售价: {row['建议零售价']}")
                
                # 验证价格字段是否正确复制
                if row['考核价'] == row['协议价'] and row['建议零售价'] == row['协议价']:
                    print(f"✅ 价格字段复制正确")
                else:
                    print(f"❌ 价格字段复制错误")
                
                # 验证日期格式
                try:
                    start_date = datetime.strptime(row['开始日期'], "%Y/%m/%d")
                    end_date = datetime.strptime(row['结束日期'], "%Y/%m/%d")
                    
                    if (end_date - start_date).days == 30:
                        print(f"✅ 日期计算正确（相差30天）")
                    else:
                        print(f"❌ 日期计算错误（相差{(end_date - start_date).days}天）")
                        
                except ValueError as e:
                    print(f"❌ 日期格式错误: {e}")
            
            print(f"\n🎉 Excel写入工具测试完成！")
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {e}")
    else:
        print(f"❌ 测试文件不存在: {test_file}")


def test_agent_integration():
    """测试智能体集成"""
    print(f"\n🤖 测试智能体集成...")
    
    try:
        from data_agent.agents.graph import get_agent_executor
        
        agent = get_agent_executor()
        
        # 测试协议价数据输入
        test_input = """
        请帮我处理以下协议价数据：
        客户ID: C999
        货品ID: P999  
        协议价: 188.88
        """
        
        print(f"📤 发送测试请求:")
        print(test_input)
        
        response = agent.invoke({
            "messages": [{"role": "user", "content": test_input}]
        })
        
        print(f"\n📥 智能体响应:")
        print(response["messages"][-1].content)
        
        # 验证文件是否更新
        test_file = Path("data/raw/底表.xlsx")
        if test_file.exists():
            df = pd.read_excel(test_file, sheet_name="协议价集合")
            print(f"\n📊 更新后的数据行数: {len(df)}")
            
            # 查找新添加的数据
            new_row = df[df['客户ID'] == 'C999']
            if not new_row.empty:
                print(f"✅ 智能体成功添加了新数据:")
                print(new_row.to_string(index=False))
            else:
                print(f"❌ 未找到智能体添加的数据")
        
        print(f"\n🎉 智能体集成测试完成！")
        
    except Exception as e:
        print(f"❌ 智能体集成测试失败: {e}")


if __name__ == '__main__':
    print("🚀 开始Excel写入工具完整测试...")
    
    # 测试Excel写入工具
    test_excel_writer_tool()
    
    # 测试智能体集成
    test_agent_integration()
    
    print(f"\n✨ 所有测试完成！")
