"""
检查现有Excel文件结构
"""
import pandas as pd
from pathlib import Path

def check_excel_structure():
    """检查Excel文件结构"""
    file_path = Path("data/raw/底表.xlsx")
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    print(f"📊 检查Excel文件结构: {file_path}")
    print("=" * 60)
    
    try:
        # 读取所有工作表
        xl = pd.ExcelFile(file_path)
        print(f"📋 工作表列表: {xl.sheet_names}")
        
        structure = {}
        
        for sheet_name in xl.sheet_names:
            print(f"\n📄 工作表: {sheet_name}")
            print("-" * 40)
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"📊 数据行数: {len(df)}")
            print(f"📊 列数: {len(df.columns)}")
            print(f"📋 表头: {list(df.columns)}")
            
            # 保存结构信息
            structure[sheet_name] = {
                'columns': list(df.columns),
                'rows': len(df),
                'data': df
            }
            
            if len(df) > 0:
                print(f"📋 前几行数据:")
                print(df.head(3).to_string(index=False))
            else:
                print(f"📋 工作表为空")
        
        return structure
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

if __name__ == '__main__':
    structure = check_excel_structure()
    
    if structure:
        print(f"\n🎯 文件结构检查完成！")
        
        # 特别关注协议价集合工作表
        if "协议价集合" in structure:
            print(f"\n🔍 协议价集合工作表详情:")
            sheet_info = structure["协议价集合"]
            print(f"表头顺序: {sheet_info['columns']}")
            print(f"数据行数: {sheet_info['rows']}")
        else:
            print(f"\n⚠️ 未找到'协议价集合'工作表")
            print(f"可用工作表: {list(structure.keys())}")
