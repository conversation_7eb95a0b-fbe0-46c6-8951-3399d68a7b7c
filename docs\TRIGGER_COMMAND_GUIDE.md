# 智能体脚本触发指令使用指南

## 🎯 指令格式

智能体现在支持特定的触发指令格式来执行Excel协议价写入脚本：

### 精确触发格式
```
客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本
```

## ✅ 正确使用示例

### 基本示例
```
客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本
```

### 更多示例
```
客户ID为CUST123,货品ID为PROD456,价格为150.50,开始执行脚本
客户ID为TEST001,货品ID为ITEM999,价格为75.25,开始执行脚本
客户ID为客户A,货品ID为产品B,价格为200.00,开始执行脚本
```

## ❌ 错误格式（不会触发）

以下格式不会触发脚本执行：

```
客户ID是C001，货品ID是P001，价格是99.99
请处理：客户ID为C001,货品ID为P001,价格为99.99
客户ID为C001,货品ID为P001,价格为99.99,请执行脚本
客户ID为C001，货品ID为P001，价格为99.99，开始执行脚本
```

## 📝 格式要求

1. **英文逗号**: 必须使用英文逗号 `,` 分隔
2. **连接词**: 必须使用 `为` 连接字段名和值
3. **价格格式**: 价格必须是数字（可含小数点）
4. **结尾词**: 必须以 `开始执行脚本` 结尾
5. **完全匹配**: 格式必须完全匹配，不能有额外文字

## 🔧 执行流程

当智能体识别到正确的触发指令时：

1. **立即解析**: 提取客户ID、货品ID、价格三个参数
2. **自动执行**: 无需确认，立即调用Excel写入工具
3. **数据处理**: 
   - 写入到"协议价集合"工作表
   - 按照现有18个字段的表头顺序填写
   - 自动计算开始日期（当前日期）
   - 自动计算结束日期（开始日期+30天）
   - 自动复制价格到考核价和建议零售价
   - 其他字段保持空值
4. **保护数据**: 所有其他工作表和表头保持不变
5. **返回结果**: 提供详细的执行反馈

## 📊 写入字段映射

| 表头字段 | 数据来源 | 示例 |
|---------|---------|------|
| 客户ID(必填) | 指令中的客户ID | C001 |
| 客户名称 | 空值 | (空) |
| 货品ID(必填) | 指令中的货品ID | P001 |
| 通用名 | 空值 | (空) |
| 商品名 | 空值 | (空) |
| 规格 | 空值 | (空) |
| 生产厂商 | 空值 | (空) |
| 产地 | 空值 | (空) |
| 开始日期(必填) | 当前日期 | 2025/07/16 |
| 结束日期(必填) | 开始日期+30天 | 2025/08/15 |
| 协议价(必填) | 指令中的价格 | 99.99 |
| 考核价 | 复制协议价 | 99.99 |
| 建议零售价 | 复制协议价 | 99.99 |
| 折扣率 | 空值 | (空) |
| 折后价 | 空值 | (空) |
| 折扣额 | 空值 | (空) |
| 档案号 | 空值 | (空) |
| 备注 | 空值 | (空) |

## 🚀 启动方法

使用支持新触发指令的智能体：

```bash
python start_final.py
```

## 💬 使用流程

1. **启动智能体**
   ```bash
   python start_final.py
   ```

2. **输入触发指令**
   ```
   用户: 客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本
   ```

3. **查看执行结果**
   ```
   🤖 Agent: ✅ 协议价数据已成功写入Excel文件！
   
   📋 写入详情：
   • 文件路径：data\raw\底表.xlsx
   • 工作表：协议价集合
   • 客户ID：C001
   • 货品ID：P001
   • 协议价：99.99
   • 开始日期：2025/07/16
   • 结束日期：2025/08/15
   • 考核价：99.99
   • 建议零售价：99.99
   
   📊 当前工作表共有 X 条记录。
   ```

## 🔍 验证方法

执行后可以通过以下方式验证：

1. **检查Excel文件**: 打开 `data/raw/底表.xlsx`
2. **查看工作表**: 切换到"协议价集合"工作表
3. **确认数据**: 验证新增的数据行
4. **检查其他工作表**: 确认其他工作表未受影响

## ⚠️ 注意事项

1. **格式严格**: 必须完全按照指定格式输入
2. **立即执行**: 识别到正确格式后立即执行，无确认步骤
3. **数据覆盖**: 新数据会追加到现有数据后面
4. **文件权限**: 确保Excel文件未被其他程序占用
5. **备份建议**: 建议在使用前备份重要的Excel文件

## 🎉 功能特点

✅ **精确识别**: 只有完全匹配的格式才会触发  
✅ **立即执行**: 无需额外确认步骤  
✅ **数据安全**: 严格保护现有数据结构  
✅ **自动计算**: 智能处理日期和价格字段  
✅ **详细反馈**: 提供完整的执行结果信息  

您的智能体现在支持精确的脚本触发指令，可以安全、高效地处理协议价数据！
