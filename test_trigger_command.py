"""
测试新的脚本触发指令格式
"""
import sys
import os
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_trigger_commands():
    """测试脚本触发指令"""
    print("🧪 测试脚本触发指令格式...")
    print("=" * 60)
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        
        # 禁用LangSmith追踪
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.2
        )
        
        # 使用Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 创建系统提示词
        system_prompt = """
        You are a helpful assistant that processes agreement price script commands.
        
        IMPORTANT: Agreement Price Script Trigger Format
        When you see the EXACT format: "客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本"
        You MUST:
        1. Extract the three values: 客户ID, 货品ID, 价格
        2. Immediately call the write_agreement_price_to_excel tool with these parameters:
           - customer_id: the value after "客户ID为"
           - product_id: the value after "货品ID为"  
           - agreement_price: the numeric value after "价格为"
        3. Do not ask for confirmation, execute immediately
        
        Examples:
        - "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本"
        - "客户ID为CUST123,货品ID为PROD456,价格为150.50,开始执行脚本"
        
        Always respond in Chinese and provide clear feedback.
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        print("✅ 智能体创建成功！")
        
        # 测试指令
        test_commands = [
            "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本",
            "客户ID为CUST123,货品ID为PROD456,价格为150.50,开始执行脚本",
            "客户ID为TEST001,货品ID为ITEM999,价格为75.25,开始执行脚本"
        ]
        
        print(f"\n📋 测试 {len(test_commands)} 条触发指令...")
        
        for i, command in enumerate(test_commands, 1):
            print(f"\n🔄 测试指令 {i}:")
            print(f"💬 输入: {command}")
            print(f"🤖 智能体响应:")
            print("-" * 50)
            
            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": command}]
                })
                
                result = response["messages"][-1].content
                print(result)
                print("-" * 50)
                print("✅ 指令执行完成")
                
            except Exception as e:
                print(f"❌ 指令执行失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_non_trigger_commands():
    """测试非触发指令"""
    print(f"\n🔍 测试非触发指令...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建智能体（简化版）
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.2
        )
        
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        system_prompt = """
        You are a helpful assistant. Only execute Excel operations when you see the exact format:
        "客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本"
        
        For other requests, just respond normally without using tools.
        Always respond in Chinese.
        """
        
        agent = create_react_agent(model=llm, tools=tools, prompt=system_prompt)
        
        # 测试非触发指令
        non_trigger_commands = [
            "你好，请介绍一下你的功能",
            "客户ID是C001，货品ID是P001，价格是99.99",  # 格式不完全匹配
            "请帮我处理协议价数据：客户ID为C001，货品ID为P001，价格为99.99",  # 不是精确格式
        ]
        
        for i, command in enumerate(non_trigger_commands, 1):
            print(f"\n🔄 非触发测试 {i}:")
            print(f"💬 输入: {command}")
            print(f"🤖 响应:")
            print("-" * 30)
            
            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": command}]
                })
                
                result = response["messages"][-1].content
                print(result)
                print("-" * 30)
                
            except Exception as e:
                print(f"❌ 响应失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 非触发测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 脚本触发指令测试")
    print("=" * 60)
    print("📋 测试目标:")
    print("✅ 验证精确指令格式触发Excel写入")
    print("✅ 验证非精确格式不会误触发")
    print("✅ 验证参数提取和传递正确")
    print("=" * 60)
    
    # 测试触发指令
    success1 = test_trigger_commands()
    
    # 测试非触发指令
    success2 = test_non_trigger_commands()
    
    if success1 and success2:
        print(f"\n🎉 所有测试通过！")
        print(f"\n📋 指令格式确认:")
        print(f"✅ 精确格式: 客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本")
        print(f"✅ 立即执行，无需确认")
        print(f"✅ 自动提取参数并调用Excel工具")
        print(f"✅ 非精确格式不会误触发")
    else:
        print(f"\n❌ 部分测试失败")

if __name__ == '__main__':
    main()
