"""
测试修复后的Excel写入工具
严格按照现有表头填写，不改动任何工作表和表头
"""
import sys
from pathlib import Path
import pandas as pd

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_fixed_excel_tool():
    """测试修复后的Excel工具"""
    print("🧪 测试修复后的Excel协议价工具...")
    print("=" * 60)
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 创建工具
        tool = create_excel_writer_tool()
        print(f"✅ Excel工具创建成功: {tool.name}")
        
        # 检查底表文件是否存在
        file_path = Path("data/raw/底表.xlsx")
        if not file_path.exists():
            print(f"❌ 底表.xlsx文件不存在: {file_path}")
            return False
        
        # 显示原始文件结构
        print(f"\n📊 原始文件结构:")
        with pd.ExcelFile(file_path) as xls:
            print(f"工作表列表: {xls.sheet_names}")
            
            # 显示协议价集合工作表的原始状态
            original_df = pd.read_excel(file_path, sheet_name="协议价集合")
            print(f"\n📋 协议价集合工作表原始状态:")
            print(f"表头: {list(original_df.columns)}")
            print(f"数据行数: {len(original_df)}")
        
        # 测试数据
        test_cases = [
            {
                "customer_id": "C001",
                "product_id": "P001",
                "agreement_price": 99.99,
                "description": "第一条测试数据"
            },
            {
                "customer_id": "C002",
                "product_id": "P002",
                "agreement_price": 150.50,
                "description": "第二条测试数据"
            }
        ]
        
        print(f"\n📋 开始测试 {len(test_cases)} 条数据...")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔄 执行测试用例 {i}: {test_case['description']}")
            print(f"   客户ID: {test_case['customer_id']}")
            print(f"   货品ID: {test_case['product_id']}")
            print(f"   协议价: {test_case['agreement_price']}")
            
            try:
                result = tool._run(
                    customer_id=test_case["customer_id"],
                    product_id=test_case["product_id"],
                    agreement_price=test_case["agreement_price"]
                )
                
                print(f"📤 测试结果:")
                print(result)
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        # 验证结果
        print(f"\n🔍 验证写入结果...")
        
        # 检查所有工作表是否保持不变
        with pd.ExcelFile(file_path) as xls:
            current_sheets = xls.sheet_names
            print(f"当前工作表列表: {current_sheets}")
            
            # 检查协议价集合工作表
            updated_df = pd.read_excel(file_path, sheet_name="协议价集合")
            print(f"\n📋 协议价集合工作表更新后状态:")
            print(f"表头: {list(updated_df.columns)}")
            print(f"数据行数: {len(updated_df)}")
            
            if len(updated_df) > 0:
                print(f"\n📋 新增数据:")
                for index, row in updated_df.iterrows():
                    print(f"第{index+1}行:")
                    print(f"  客户ID(必填): {row['客户ID(必填)']}")
                    print(f"  货品ID(必填): {row['货品ID(必填)']}")
                    print(f"  协议价(必填): {row['协议价(必填)']}")
                    print(f"  开始日期(必填): {row['开始日期(必填)']}")
                    print(f"  结束日期(必填): {row['结束日期(必填)']}")
                    print(f"  考核价: {row['考核价']}")
                    print(f"  建议零售价: {row['建议零售价']}")
                    print()
            
            # 验证其他工作表是否保持不变
            print(f"🔍 验证其他工作表是否保持不变...")
            for sheet_name in current_sheets:
                if sheet_name != "协议价集合":
                    sheet_df = pd.read_excel(file_path, sheet_name=sheet_name)
                    print(f"  {sheet_name}: {len(sheet_df)} 行数据")
        
        print(f"\n✅ 测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 修复后的Excel协议价工具测试")
    print("=" * 60)
    print("📋 测试目标:")
    print("✅ 严格按照现有表头顺序填写数据")
    print("✅ 不改动任何工作表")
    print("✅ 不改动任何表头")
    print("✅ 只在协议价集合工作表中添加数据")
    print("=" * 60)
    
    success = test_fixed_excel_tool()
    
    if success:
        print(f"\n🎉 所有测试通过！")
        print(f"\n📋 功能确认:")
        print(f"✅ 严格按照现有18个字段的表头填写")
        print(f"✅ 必填字段正确填写：客户ID(必填)、货品ID(必填)、协议价(必填)、开始日期(必填)、结束日期(必填)")
        print(f"✅ 自动复制字段：考核价、建议零售价")
        print(f"✅ 其他字段保持空值")
        print(f"✅ 所有其他工作表保持不变")
    else:
        print(f"\n❌ 测试失败，请检查错误信息")

if __name__ == '__main__':
    main()
