# 协议价功能升级说明

## 概述

本次升级为Data Agent智能体添加了协议价数据处理功能，使其能够接收用户输入的协议价信息并自动写入到Excel文件中，同时保持原有的MySQL数据库查询功能。

## 新增功能

### 1. 协议价数据输入处理
- **客户ID** (必填) - 从用户输入获取
- **货品ID** (必填) - 从用户输入获取  
- **协议价** (必填) - 从用户输入获取
- **开始日期** (自动生成) - 当前日期，格式：YYYY/MM/DD
- **结束日期** (自动计算) - 开始日期+30天，格式：YYYY/MM/DD
- **考核价** (自动复制) - 复制协议价的值
- **建议零售价** (自动复制) - 复制协议价的值

### 2. Excel文件操作
- **文件名**: 底表.xlsx
- **保存位置**: data/raw/底表.xlsx
- **工作表名**: 协议价集合
- **支持多工作表**: 保留现有其他工作表内容

## 技术实现

### 新增文件

1. **src/data_agent/tools/excel_writer.py**
   - Excel写入工具实现
   - 协议价数据模型定义
   - 自动日期计算和价格复制逻辑

2. **tests/test_excel_writer.py**
   - Excel工具测试用例
   - 智能体集成测试

3. **demo_agreement_price.py**
   - 功能演示脚本
   - 使用示例展示

### 修改文件

1. **src/data_agent/tools/all_tools.py**
   - 集成Excel写入工具到工具集合

2. **src/data_agent/agents/graph.py**
   - 更新智能体提示词
   - 添加协议价识别和处理指令

## 使用方法

### 1. 基本协议价输入

```
用户输入：
请帮我处理以下协议价数据：
客户ID: C001
货品ID: P001
协议价: 99.99

智能体响应：
✅ 协议价数据已成功写入Excel文件！
📋 写入详情：
• 文件路径：data/raw/底表.xlsx
• 工作表：协议价集合
• 客户ID：C001
• 货品ID：P001
• 协议价：99.99
• 开始日期：2025/01/15
• 结束日期：2025/02/14
• 考核价：99.99
• 建议零售价：99.99
```

### 2. 多条数据处理

```
用户输入：
请处理这些协议价信息：
1. 客户ID: C002, 货品ID: P002, 协议价: 150.50
2. 客户ID: C003, 货品ID: P003, 协议价: 75.25

智能体将：
- 逐条处理每个协议价数据
- 分别写入Excel文件
- 自动计算所有日期和价格字段
```

### 3. 混合功能使用

```
用户输入：
先查询一下技术部的员工数量，然后处理这个协议价：
客户ID: C004, 货品ID: P004, 协议价: 200.00

智能体将：
1. 使用SQL工具查询技术部员工数量
2. 使用Excel工具处理协议价数据
3. 提供完整的响应结果
```

## 工具详情

### Excel写入工具 (write_agreement_price_to_excel)

**参数：**
- `customer_id` (str): 客户ID，必填
- `product_id` (str): 货品ID，必填
- `agreement_price` (float): 协议价，必填

**自动处理：**
- 开始日期：当前日期 (YYYY/MM/DD)
- 结束日期：开始日期 + 30天 (YYYY/MM/DD)
- 考核价：复制协议价值
- 建议零售价：复制协议价值

**文件操作：**
- 如果文件不存在，创建新文件
- 如果文件存在，追加数据到指定工作表
- 保留其他工作表内容不变
- 处理文件占用情况（自动关闭Excel进程）

## 智能体提示词更新

智能体现在能够：
1. 识别协议价数据输入模式
2. 提取必需的三个字段（客户ID、货品ID、协议价）
3. 自动调用Excel写入工具
4. 保持原有的SQL查询和Web搜索功能

## 兼容性

- ✅ 保持原有MySQL数据库查询功能
- ✅ 保持原有Web搜索功能  
- ✅ 支持混合功能使用（同时查询数据库和处理协议价）
- ✅ 向后兼容现有API接口

## 测试验证

### 运行演示
```bash
python demo_agreement_price.py
```

### 运行测试
```bash
python tests/test_excel_writer.py
```

### 交互式测试
```bash
python main.py interactive
```

然后输入协议价数据进行测试。

## 注意事项

1. **文件权限**: 确保data/raw目录有写入权限
2. **Excel进程**: 如果Excel文件被占用，工具会自动尝试关闭Excel进程
3. **数据格式**: 协议价必须是数字格式
4. **日期格式**: 自动生成的日期使用YYYY/MM/DD格式
5. **工作表**: 数据写入到"协议价集合"工作表，其他工作表保持不变

## 故障排除

### 常见问题

1. **权限错误**
   - 检查data/raw目录权限
   - 关闭可能打开的Excel文件
   - 以管理员权限运行

2. **导入错误**
   - 确保所有依赖包已安装
   - 检查Python路径配置

3. **数据格式错误**
   - 确保协议价是有效数字
   - 检查客户ID和货品ID格式

## 升级完成

🎉 Data Agent现在支持：
- ✅ MySQL数据库查询
- ✅ Web搜索功能
- ✅ 协议价Excel写入
- ✅ 混合功能使用
- ✅ 自动数据处理和计算
