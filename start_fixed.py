"""
修复编码问题的Data Agent启动脚本
"""
import sys
import os
import locale
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# 设置locale
try:
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
    except:
        pass

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def create_simple_agent():
    """创建简化的智能体（避免数据库连接问题）"""
    print("🤖 正在创建智能体...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        
        # 禁用LangSmith追踪以避免编码问题
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.2
        )
        
        # 仅使用Excel工具（避免数据库连接问题）
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 创建系统提示词
        system_prompt = """
        You are a helpful assistant that specializes in processing agreement price data.
        
        When you receive agreement price data with Customer ID, Product ID, and Agreement Price, 
        use the Excel writer tool to save this data.
        
        Look for these required fields:
        - 客户ID (Customer ID) - required
        - 货品ID (Product ID) - required  
        - 协议价 (Agreement Price) - required
        
        The tool will automatically:
        - Set start date to current date (YYYY/MM/DD format)
        - Set end date to start date + 30 days (YYYY/MM/DD format)
        - Copy agreement price to assessment price (考核价)
        - Copy agreement price to suggested retail price (建议零售价)
        - Save data to "协议价集合" sheet in "底表.xlsx" file
        
        Always respond in Chinese and provide clear feedback about the operation.
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        print("✅ 智能体创建成功！")
        return agent
        
    except Exception as e:
        print(f"❌ 智能体创建失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("🚀 Data Agent 启动（修复版）")
    print("=" * 50)
    
    # 显示编码信息
    print(f"📝 系统编码: {sys.getdefaultencoding()}")
    print(f"📝 输出编码: {sys.stdout.encoding}")
    
    # 创建智能体
    agent = create_simple_agent()
    if not agent:
        print("❌ 启动失败")
        return
    
    # 显示功能介绍
    print("\n🎯 协议价处理功能:")
    print("• 自动识别协议价数据")
    print("• 写入Excel文件（底表.xlsx）")
    print("• 自动计算日期和价格字段")
    
    print("\n💡 使用示例:")
    print("请帮我处理以下协议价数据：")
    print("客户ID: C001")
    print("货品ID: P001")
    print("协议价: 99.99")
    
    print("\n" + "=" * 50)
    print("🤖 Data Agent 交互模式")
    print("输入 'quit' 或 'exit' 退出")
    print("=" * 50)
    
    # 交互循环
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print("🤖 Agent: ", end="", flush=True)
            
            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": user_input}]
                })
                
                # 安全地输出响应
                result = response["messages"][-1].content
                print(result)
                
            except UnicodeEncodeError as e:
                print(f"编码错误，但处理可能已完成。请检查Excel文件。")
            except Exception as e:
                print(f"处理错误: {str(e)}")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 输入错误: {str(e)}")

if __name__ == '__main__':
    main()
