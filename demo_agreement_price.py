"""
协议价功能演示脚本
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def demo_excel_tool():
    """演示Excel工具功能"""
    print("🎯 协议价Excel写入工具演示")
    print("=" * 50)
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 创建工具
        tool = create_excel_writer_tool()
        print(f"✅ Excel工具创建成功")
        print(f"📋 工具名称: {tool.name}")
        print(f"📝 工具描述: {tool.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel工具创建失败: {e}")
        return False

def demo_agent_integration():
    """演示智能体集成"""
    print("\n🤖 智能体集成演示")
    print("=" * 50)
    
    try:
        from data_agent.tools.all_tools import get_all_tools
        
        # 获取所有工具
        tools = get_all_tools()
        print(f"✅ 工具集合加载成功")
        print(f"📊 总工具数量: {len(tools)}")
        
        # 查找Excel工具
        excel_tool = None
        for tool in tools:
            if tool.name == "write_agreement_price_to_excel":
                excel_tool = tool
                break
        
        if excel_tool:
            print(f"✅ 找到Excel写入工具: {excel_tool.name}")
        else:
            print(f"❌ 未找到Excel写入工具")
            return False
        
        # 显示所有工具
        print(f"\n📋 可用工具列表:")
        for i, tool in enumerate(tools, 1):
            print(f"  {i}. {tool.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体集成失败: {e}")
        return False

def demo_agent_prompt():
    """演示智能体提示词"""
    print("\n📝 智能体提示词演示")
    print("=" * 50)
    
    try:
        from data_agent.agents.graph import create_agent_executor
        
        # 创建智能体
        agent = create_agent_executor()
        print(f"✅ 智能体创建成功")
        
        # 显示提示词（如果可以访问的话）
        print(f"✅ 智能体已配置协议价处理功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体创建失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 使用示例")
    print("=" * 50)
    
    examples = [
        {
            "title": "基本协议价输入",
            "input": """
请帮我处理以下协议价数据：
客户ID: C001
货品ID: P001
协议价: 99.99
            """.strip()
        },
        {
            "title": "多条协议价数据",
            "input": """
请处理这些协议价信息：
1. 客户ID: C002, 货品ID: P002, 协议价: 150.50
2. 客户ID: C003, 货品ID: P003, 协议价: 75.25
            """.strip()
        },
        {
            "title": "混合查询和数据输入",
            "input": """
先查询一下技术部的员工数量，然后处理这个协议价：
客户ID: C004, 货品ID: P004, 协议价: 200.00
            """.strip()
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n📋 示例 {i}: {example['title']}")
        print(f"💬 用户输入:")
        print(f"   {example['input']}")
        print(f"🔧 智能体将:")
        print(f"   - 识别协议价数据")
        print(f"   - 调用Excel写入工具")
        print(f"   - 自动计算开始/结束日期")
        print(f"   - 复制价格到考核价和建议零售价")
        print(f"   - 保存到'底表.xlsx'的'协议价集合'工作表")

def main():
    """主函数"""
    print("🚀 Data Agent 协议价功能升级演示")
    print("=" * 60)
    
    # 演示各个组件
    success1 = demo_excel_tool()
    success2 = demo_agent_integration() 
    success3 = demo_agent_prompt()
    
    # 显示使用示例
    show_usage_examples()
    
    # 总结
    print(f"\n📊 升级总结")
    print("=" * 50)
    
    if success1 and success2 and success3:
        print("✅ 所有组件升级成功！")
        print("\n🎯 新增功能:")
        print("  • Excel协议价数据写入工具")
        print("  • 智能体协议价识别和处理")
        print("  • 自动日期计算（开始日期+30天）")
        print("  • 自动价格字段复制")
        print("  • 保持原有MySQL查询功能")
        
        print(f"\n🔧 技术实现:")
        print("  • 新增 excel_writer.py 工具模块")
        print("  • 集成到 all_tools.py 工具集合")
        print("  • 更新智能体提示词支持协议价识别")
        print("  • 使用 openpyxl 引擎处理Excel文件")
        print("  • 支持多工作表Excel文件")
        
        print(f"\n📁 文件结构:")
        print("  • 数据保存到: data/raw/底表.xlsx")
        print("  • 工作表名称: 协议价集合")
        print("  • 字段: 客户ID, 货品ID, 协议价, 开始日期, 结束日期, 考核价, 建议零售价")
        
        print(f"\n🎉 升级完成！智能体现在可以同时处理MySQL查询和协议价Excel写入。")
        
    else:
        print("❌ 部分组件升级失败，请检查错误信息。")

if __name__ == '__main__':
    main()
