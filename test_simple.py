import sys
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, 'src')

def test_simple_excel_write():
    """简单测试Excel写入"""
    print("🧪 开始简单Excel写入测试...")
    
    # 准备数据
    current_date = datetime.now()
    start_date = current_date.strftime("%Y/%m/%d")
    end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
    
    new_row = {
        "客户ID": "C001",
        "货品ID": "P001", 
        "协议价": 99.99,
        "开始日期": start_date,
        "结束日期": end_date,
        "考核价": 99.99,
        "建议零售价": 99.99
    }
    
    df = pd.DataFrame([new_row])
    
    # 创建测试文件
    file_path = Path("data/raw/test_agreement.xlsx")
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='协议价集合', index=False)
        
        print(f"✅ 测试文件创建成功: {file_path}")
        
        # 验证文件
        test_df = pd.read_excel(file_path, sheet_name='协议价集合')
        print(f"📊 验证结果:")
        print(test_df.to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_excel_tool():
    """测试Excel工具"""
    print("\n🔧 测试Excel工具...")
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        tool = create_excel_writer_tool()
        print(f"✅ 工具创建成功: {tool.name}")
        
        # 修改工具使用测试文件
        original_run = tool._run
        
        def test_run(customer_id, product_id, agreement_price):
            # 临时修改文件路径
            import tempfile
            import os
            
            # 使用临时文件
            temp_dir = Path("temp_test")
            temp_dir.mkdir(exist_ok=True)
            test_file = temp_dir / "test_底表.xlsx"
            
            try:
                # 准备数据
                current_date = datetime.now()
                start_date = current_date.strftime("%Y/%m/%d")
                end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
                
                new_row = {
                    "客户ID": customer_id,
                    "货品ID": product_id,
                    "协议价": agreement_price,
                    "开始日期": start_date,
                    "结束日期": end_date,
                    "考核价": agreement_price,
                    "建议零售价": agreement_price
                }
                
                df = pd.DataFrame([new_row])
                
                # 写入文件
                with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='协议价集合', index=False)
                
                return f"""✅ 协议价数据已成功写入Excel文件！

📋 写入详情：
• 文件路径：{test_file}
• 工作表：协议价集合
• 客户ID：{customer_id}
• 货品ID：{product_id}
• 协议价：{agreement_price}
• 开始日期：{start_date}
• 结束日期：{end_date}
• 考核价：{agreement_price}
• 建议零售价：{agreement_price}

📊 当前工作表共有 1 条记录。"""
                
            except Exception as e:
                return f"❌ 处理协议价数据时发生错误：{str(e)}"
        
        # 测试工具
        result = test_run("C001", "P001", 99.99)
        print("📤 工具测试结果:")
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 开始测试...")
    
    # 简单Excel写入测试
    success1 = test_simple_excel_write()
    
    # Excel工具测试
    success2 = test_excel_tool()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败")
