"""
简化的Data Agent启动脚本
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    print("🚀 启动 Data Agent...")
    print("=" * 50)
    
    try:
        # 导入智能体
        print("📦 正在加载智能体...")
        from data_agent.agents.graph import get_agent_executor
        
        agent = get_agent_executor()
        print("✅ 智能体加载成功！")
        
        # 显示功能介绍
        print("\n🎯 Data Agent 功能:")
        print("1. 📊 MySQL数据库查询")
        print("2. 🔍 Web搜索")
        print("3. 📝 协议价Excel写入")
        
        print("\n💡 协议价使用示例:")
        print("请帮我处理以下协议价数据：")
        print("客户ID: C001")
        print("货品ID: P001")
        print("协议价: 99.99")
        
        print("\n" + "=" * 50)
        print("🤖 Data Agent 交互式模式")
        print("输入 'quit' 或 'exit' 退出")
        print("=" * 50)
        
        # 交互循环
        while True:
            try:
                user_input = input("\n用户: ").strip()
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                print("🤖 Agent: ", end="", flush=True)
                response = agent.invoke({
                    "messages": [{"role": "user", "content": user_input}]
                })
                print(response["messages"][-1].content)
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {str(e)}")
                print("请重试或输入 'quit' 退出")
    
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        print("\n🔧 故障排除:")
        print("1. 检查 .env 文件配置")
        print("2. 确保所有依赖包已安装")
        print("3. 检查数据库连接")

if __name__ == '__main__':
    main()
