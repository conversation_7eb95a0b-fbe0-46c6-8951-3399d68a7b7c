"""
最终版Data Agent启动脚本
使用修复后的Excel写入工具，严格按照现有表头填写
"""
import sys
import os
import locale
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
try:
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')
except:
    pass

# 设置locale
try:
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
    except:
        pass

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查底表文件
    excel_file = Path("data/raw/底表.xlsx")
    if not excel_file.exists():
        print(f"❌ 底表.xlsx文件不存在: {excel_file}")
        print(f"请确保文件存在于指定位置")
        return False
    
    print(f"✅ 底表.xlsx文件存在: {excel_file}")
    
    # 检查协议价集合工作表
    try:
        import pandas as pd
        df = pd.read_excel(excel_file, sheet_name="协议价集合")
        print(f"✅ 协议价集合工作表存在，表头字段: {len(df.columns)} 个")
        return True
    except Exception as e:
        print(f"❌ 协议价集合工作表检查失败: {e}")
        return False

def create_agent():
    """创建智能体"""
    print("🤖 正在创建智能体...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        
        # 禁用LangSmith追踪以避免编码问题
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.2
        )
        
        # 使用修复后的Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 创建系统提示词
        system_prompt = """
        You are a helpful assistant that specializes in processing agreement price data.
        
        When you receive agreement price data, look for these required fields:
        - 客户ID (Customer ID) - required
        - 货品ID (Product ID) - required  
        - 协议价 (Agreement Price) - required
        
        Use the Excel writer tool to save this data. The tool will:
        - Write data to the existing "协议价集合" sheet in "底表.xlsx"
        - Follow the exact existing column headers (18 columns total)
        - Fill required fields: 客户ID(必填), 货品ID(必填), 协议价(必填), 开始日期(必填), 结束日期(必填)
        - Auto-calculate: 开始日期 (current date), 结束日期 (start date + 30 days)
        - Auto-copy: 考核价 and 建议零售价 (copy from 协议价)
        - Leave other fields empty
        - Preserve all other worksheets and headers unchanged
        
        Always respond in Chinese and provide clear feedback about the operation.
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        print("✅ 智能体创建成功！")
        return agent
        
    except Exception as e:
        print(f"❌ 智能体创建失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("🚀 Data Agent 最终版启动")
    print("=" * 60)
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请解决问题后重试")
        return
    
    # 创建智能体
    agent = create_agent()
    if not agent:
        print("❌ 启动失败")
        return
    
    # 显示功能介绍
    print("\n🎯 协议价处理功能:")
    print("• 严格按照现有表头顺序填写数据")
    print("• 不改动任何工作表和表头")
    print("• 自动计算日期字段（开始日期+30天）")
    print("• 自动复制价格字段（考核价、建议零售价）")
    print("• 保持其他工作表完全不变")
    
    print("\n💡 使用示例:")
    print("请帮我处理以下协议价数据：")
    print("客户ID: C001")
    print("货品ID: P001")
    print("协议价: 99.99")
    
    print("\n📋 支持的表头字段（18个）:")
    print("客户ID(必填), 客户名称, 货品ID(必填), 通用名, 商品名, 规格,")
    print("生产厂商, 产地, 开始日期(必填), 结束日期(必填), 协议价(必填),")
    print("考核价, 建议零售价, 折扣率, 折后价, 折扣额, 档案号, 备注")
    
    print("\n" + "=" * 60)
    print("🤖 Data Agent 交互模式")
    print("输入 'quit' 或 'exit' 退出")
    print("=" * 60)
    
    # 交互循环
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print("🤖 Agent: ", end="", flush=True)
            
            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": user_input}]
                })
                
                # 安全地输出响应
                result = response["messages"][-1].content
                print(result)
                
            except UnicodeEncodeError as e:
                print(f"编码错误，但处理可能已完成。请检查Excel文件。")
            except Exception as e:
                print(f"处理错误: {str(e)}")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 输入错误: {str(e)}")

if __name__ == '__main__':
    main()
