"""
直接测试Excel工具功能（绕过智能体）
"""
import sys
import os
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_excel_tool_direct():
    """直接测试Excel工具"""
    print("🧪 直接测试Excel协议价工具...")
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 创建工具
        tool = create_excel_writer_tool()
        print(f"✅ Excel工具创建成功: {tool.name}")
        
        # 测试数据
        test_cases = [
            {
                "customer_id": "C001",
                "product_id": "P001", 
                "agreement_price": 99.99,
                "description": "第一条测试数据"
            },
            {
                "customer_id": "C002",
                "product_id": "P002",
                "agreement_price": 150.50,
                "description": "第二条测试数据"
            },
            {
                "customer_id": "C003",
                "product_id": "P003",
                "agreement_price": 75.25,
                "description": "第三条测试数据"
            }
        ]
        
        print(f"\n📋 准备测试 {len(test_cases)} 条数据...")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔄 执行测试用例 {i}: {test_case['description']}")
            print(f"   客户ID: {test_case['customer_id']}")
            print(f"   货品ID: {test_case['product_id']}")
            print(f"   协议价: {test_case['agreement_price']}")
            
            try:
                result = tool._run(
                    customer_id=test_case["customer_id"],
                    product_id=test_case["product_id"],
                    agreement_price=test_case["agreement_price"]
                )
                
                print(f"📤 测试结果:")
                print(result)
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        # 验证文件
        excel_file = Path("data/raw/底表.xlsx")
        if excel_file.exists():
            print(f"\n✅ Excel文件已创建: {excel_file}")
            
            try:
                import pandas as pd
                df = pd.read_excel(excel_file, sheet_name="协议价集合")
                print(f"📊 文件验证:")
                print(f"   工作表: 协议价集合")
                print(f"   数据行数: {len(df)}")
                print(f"   列数: {len(df.columns)}")
                print(f"   列名: {list(df.columns)}")
                
                print(f"\n📋 数据内容:")
                for index, row in df.iterrows():
                    print(f"   第{index+1}行: 客户ID={row['客户ID']}, 货品ID={row['货品ID']}, 协议价={row['协议价']}")
                
            except Exception as e:
                print(f"❌ 文件验证失败: {e}")
        else:
            print(f"❌ Excel文件未创建")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel工具测试失败: {e}")
        return False

def interactive_excel_test():
    """交互式Excel测试"""
    print("\n💬 交互式Excel测试模式")
    print("=" * 50)
    print("直接输入协议价数据，格式：客户ID,货品ID,协议价")
    print("例如：C001,P001,99.99")
    print("输入 'quit' 退出")
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        tool = create_excel_writer_tool()
        print("✅ Excel工具已准备就绪")
        
        while True:
            try:
                user_input = input("\n输入数据: ").strip()
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                # 解析输入
                parts = user_input.split(',')
                if len(parts) != 3:
                    print("❌ 格式错误，请使用：客户ID,货品ID,协议价")
                    continue
                
                customer_id = parts[0].strip()
                product_id = parts[1].strip()
                try:
                    agreement_price = float(parts[2].strip())
                except ValueError:
                    print("❌ 协议价必须是数字")
                    continue
                
                print(f"🔄 处理数据: 客户ID={customer_id}, 货品ID={product_id}, 协议价={agreement_price}")
                
                result = tool._run(
                    customer_id=customer_id,
                    product_id=product_id,
                    agreement_price=agreement_price
                )
                
                print("✅ 处理结果:")
                print(result)
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 处理错误: {str(e)}")
    
    except Exception as e:
        print(f"❌ 交互式测试启动失败: {e}")

def main():
    """主函数"""
    print("🚀 Excel协议价工具直接测试")
    print("=" * 60)
    
    # 直接测试Excel工具
    success = test_excel_tool_direct()
    
    if success:
        print("\n🎉 Excel工具测试成功！")
        
        # 询问是否进行交互式测试
        print("\n请选择:")
        print("1. 交互式测试")
        print("2. 退出")
        
        choice = input("\n请输入选择 (1/2): ").strip()
        
        if choice == "1":
            interactive_excel_test()
        else:
            print("👋 再见！")
    else:
        print("\n❌ Excel工具测试失败")

if __name__ == '__main__':
    main()
