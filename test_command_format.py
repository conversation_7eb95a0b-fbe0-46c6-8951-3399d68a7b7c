"""
测试指令格式解析
"""
import re

def test_command_parsing():
    """测试指令格式解析"""
    print("🧪 测试指令格式解析...")
    
    # 定义指令格式的正则表达式
    pattern = r"客户ID为([^,]+),货品ID为([^,]+),价格为([0-9.]+),开始执行脚本"
    
    # 测试指令
    test_commands = [
        "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本",
        "客户ID为CUST123,货品ID为PROD456,价格为150.50,开始执行脚本",
        "客户ID为TEST001,货品ID为ITEM999,价格为75.25,开始执行脚本",
        "客户ID为客户A,货品ID为产品B,价格为200.00,开始执行脚本",
        # 非匹配格式
        "客户ID是C001，货品ID是P001，价格是99.99",
        "请处理：客户ID为C001,货品ID为P001,价格为99.99",
        "客户ID为C001,货品ID为P001,价格为99.99,请执行脚本",
    ]
    
    print(f"📋 测试 {len(test_commands)} 条指令...")
    
    for i, command in enumerate(test_commands, 1):
        print(f"\n🔄 测试 {i}: {command}")
        
        match = re.search(pattern, command)
        if match:
            customer_id = match.group(1)
            product_id = match.group(2)
            price = float(match.group(3))
            
            print(f"✅ 匹配成功!")
            print(f"   客户ID: {customer_id}")
            print(f"   货品ID: {product_id}")
            print(f"   价格: {price}")
            
            # 模拟调用Excel工具
            print(f"   🔧 将调用Excel工具写入数据...")
            
        else:
            print(f"❌ 格式不匹配，不会触发脚本")

def create_instruction_guide():
    """创建指令使用指南"""
    print(f"\n📋 指令使用指南")
    print("=" * 50)
    
    print(f"🎯 精确触发格式:")
    print(f"客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本")
    
    print(f"\n✅ 正确示例:")
    examples = [
        "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本",
        "客户ID为CUST123,货品ID为PROD456,价格为150.50,开始执行脚本",
        "客户ID为客户A,货品ID为产品B,价格为200.00,开始执行脚本"
    ]
    
    for example in examples:
        print(f"  {example}")
    
    print(f"\n❌ 错误格式（不会触发）:")
    wrong_examples = [
        "客户ID是C001，货品ID是P001，价格是99.99",
        "请处理：客户ID为C001,货品ID为P001,价格为99.99",
        "客户ID为C001,货品ID为P001,价格为99.99,请执行脚本",
        "客户ID为C001，货品ID为P001，价格为99.99，开始执行脚本"  # 中文逗号
    ]
    
    for wrong in wrong_examples:
        print(f"  {wrong}")
    
    print(f"\n📝 格式要求:")
    print(f"1. 必须使用英文逗号 ','")
    print(f"2. 必须使用 '为' 连接")
    print(f"3. 价格必须是数字（可含小数点）")
    print(f"4. 必须以 '开始执行脚本' 结尾")
    print(f"5. 格式必须完全匹配，不能有额外文字")

def main():
    """主函数"""
    print("🚀 指令格式测试")
    print("=" * 60)
    
    # 测试指令解析
    test_command_parsing()
    
    # 创建使用指南
    create_instruction_guide()
    
    print(f"\n🎉 指令格式测试完成！")

if __name__ == '__main__':
    main()
