"""
手动创建协议价数据演示
"""
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path

def create_agreement_price_data():
    """手动创建协议价数据"""
    print("📝 手动创建协议价数据演示...")
    
    # 准备数据
    current_date = datetime.now()
    start_date = current_date.strftime("%Y/%m/%d")
    end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
    
    # 示例数据
    data = [
        {
            "客户ID": "C001",
            "货品ID": "P001",
            "协议价": 99.99,
            "开始日期": start_date,
            "结束日期": end_date,
            "考核价": 99.99,
            "建议零售价": 99.99
        },
        {
            "客户ID": "C002", 
            "货品ID": "P002",
            "协议价": 150.50,
            "开始日期": start_date,
            "结束日期": end_date,
            "考核价": 150.50,
            "建议零售价": 150.50
        },
        {
            "客户ID": "C003",
            "货品ID": "P003", 
            "协议价": 75.25,
            "开始日期": start_date,
            "结束日期": end_date,
            "考核价": 75.25,
            "建议零售价": 75.25
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 确保目录存在
    file_path = Path("data/raw/底表.xlsx")
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # 写入Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='协议价集合', index=False)
        
        print(f"✅ 协议价数据已成功创建！")
        print(f"📁 文件位置: {file_path}")
        print(f"📊 工作表: 协议价集合")
        print(f"📋 数据行数: {len(df)}")
        
        print(f"\n📋 数据内容:")
        print(df.to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def verify_excel_file():
    """验证Excel文件"""
    print(f"\n🔍 验证Excel文件...")
    
    file_path = Path("data/raw/底表.xlsx")
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path, sheet_name='协议价集合')
        
        print(f"✅ 文件验证成功!")
        print(f"📊 工作表: 协议价集合")
        print(f"📋 数据行数: {len(df)}")
        print(f"📋 列数: {len(df.columns)}")
        print(f"📋 列名: {list(df.columns)}")
        
        print(f"\n📋 数据内容:")
        for index, row in df.iterrows():
            print(f"第{index+1}行:")
            print(f"  客户ID: {row['客户ID']}")
            print(f"  货品ID: {row['货品ID']}")
            print(f"  协议价: {row['协议价']}")
            print(f"  开始日期: {row['开始日期']}")
            print(f"  结束日期: {row['结束日期']}")
            print(f"  考核价: {row['考核价']}")
            print(f"  建议零售价: {row['建议零售价']}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 协议价数据手动创建演示")
    print("=" * 60)
    
    # 创建数据
    success = create_agreement_price_data()
    
    if success:
        # 验证数据
        verify_excel_file()
        
        print(f"\n🎉 演示完成！")
        print(f"\n📋 功能说明:")
        print(f"✅ 自动计算开始日期（当前日期）")
        print(f"✅ 自动计算结束日期（开始日期+30天）")
        print(f"✅ 自动复制协议价到考核价")
        print(f"✅ 自动复制协议价到建议零售价")
        print(f"✅ 保存到指定Excel文件和工作表")
        
        print(f"\n🔧 这就是智能体协议价功能的实际效果！")
        
    else:
        print(f"\n❌ 演示失败")

if __name__ == '__main__':
    main()
